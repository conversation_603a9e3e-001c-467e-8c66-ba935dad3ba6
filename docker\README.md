# HRMS Docker Development Setup

This Docker setup is configured to use your **local workspace code** instead of pulling from the repository.

## What's Changed

- ✅ Uses your local HRMS code from the workspace
- ✅ Mounts frontend, roster, and frappe-ui directories for development
- ✅ Persists site data between container restarts
- ✅ Automatically installs frontend dependencies
- ✅ Enables developer mode by default

## Quick Start

1. **Navigate to the docker directory:**
   ```bash
   cd docker
   ```

2. **Start the services:**
   ```bash
   docker-compose up -d
   ```

3. **Watch the logs (optional):**
   ```bash
   docker-compose logs -f frappe
   ```

4. **Access the application:**
   - HRMS: http://localhost:8000
   - Login: Administrator / admin

## How It Works

### Local Code Usage
- The `init.sh` script now uses `bench get-app /workspace` instead of downloading from GitHub
- Your local HRMS code is mounted at `/workspace` inside the container
- Changes to your local code will be reflected in the running application

### Volume Mounts
- `..:/workspace` - Your entire project directory
- `frappe-sites:/home/<USER>/frappe-bench/sites` - Persists site data
- Frontend directories are mounted separately for development

### Development Features
- Developer mode is enabled automatically
- Frontend dependencies are installed automatically
- Assets are built during initialization

## Useful Commands

### Restart services:
```bash
docker-compose restart
```

### Rebuild and restart:
```bash
docker-compose down
docker-compose up --build -d
```

### Access the container shell:
```bash
docker-compose exec frappe bash
```

### View logs:
```bash
docker-compose logs -f frappe
```

### Stop services:
```bash
docker-compose down
```

## Troubleshooting

### If the container fails to start:
1. Check logs: `docker-compose logs frappe`
2. Ensure your local code is valid
3. Try rebuilding: `docker-compose up --build`

### If changes aren't reflected:
1. Restart the frappe service: `docker-compose restart frappe`
2. Clear cache inside container: `docker-compose exec frappe bench clear-cache`

### Reset everything:
```bash
docker-compose down -v  # This will delete all data!
docker-compose up -d
```

## Files Modified

- `docker-compose.yml` - Updated volume mounts and dependencies
- `docker-compose.override.yml` - Development-specific configurations
- `init.sh` - Modified to use local code and handle development setup

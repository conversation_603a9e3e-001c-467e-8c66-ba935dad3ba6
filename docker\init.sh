#!/bin/bash

# Check if bench already exists
if [ -d "/home/<USER>/frappe-bench/apps/frappe" ]; then
    echo "Bench already exists, skipping init"
    cd frappe-bench

    # Check if HRMS app is already installed from local workspace
    if [ ! -d "/home/<USER>/frappe-bench/apps/hrms" ]; then
        echo "Adding local HRMS app to existing bench..."
        bench get-app /workspace
        bench --site hrms.localhost install-app hrms
    fi

    # Start the bench
    bench start
    exit 0
else
    echo "Creating new bench..."
fi

export PATH="${NVM_DIR}/versions/node/v${NODE_VERSION_DEVELOP}/bin/:${PATH}"

bench init --skip-redis-config-generation frappe-bench

cd frappe-bench

# Use containers instead of localhost
bench set-mariadb-host mariadb
bench set-redis-cache-host redis://redis:6379
bench set-redis-queue-host redis://redis:6379
bench set-redis-socketio-host redis://redis:6379

# Remove redis, watch from <PERSON><PERSON><PERSON>le
sed -i '/redis/d' ./Procfile
sed -i '/watch/d' ./Procfile

bench get-app erpnext
# Use local HRMS code from workspace instead of downloading from repository
bench get-app /workspace

bench new-site hrms.localhost \
    --force \
    --mariadb-root-password 123 \
    --admin-password admin \
    --no-mariadb-socket

bench --site hrms.localhost install-app hrms
bench --site hrms.localhost set-config developer_mode 1
bench --site hrms.localhost enable-scheduler
bench --site hrms.localhost clear-cache
bench use hrms.localhost

# Install frontend dependencies if they exist
if [ -d "/workspace/frontend" ]; then
    echo "Installing frontend dependencies..."
    cd /workspace/frontend && yarn install --check-files
fi

if [ -d "/workspace/roster" ]; then
    echo "Installing roster dependencies..."
    cd /workspace/roster && yarn install --check-files
fi

if [ -d "/workspace/frappe-ui" ]; then
    echo "Installing frappe-ui dependencies..."
    cd /workspace/frappe-ui && yarn install --check-files
fi

# Return to bench directory
cd /home/<USER>/frappe-bench

# Build assets for development
echo "Building assets..."
bench build --app hrms

bench start

# Development overrides for docker-compose.yml
# This file is automatically loaded by docker-compose and provides
# development-specific configurations

services:
  frappe:
    environment:
      - SHELL=/bin/bash
      - DEVELOPER_MODE=1
    volumes:
      # Additional volume mounts for development
      - ../frontend:/workspace/frontend:cached # Frontend development files
      - ../roster:/workspace/roster:cached # Roster development files
      - ../frappe-ui:/workspace/frappe-ui:cached # Frappe UI development files
      # Mount node_modules separately to avoid conflicts
      - frappe-node-modules:/workspace/frontend/node_modules
      - roster-node-modules:/workspace/roster/node_modules
      - frappe-ui-node-modules:/workspace/frappe-ui/node_modules
    # Enable hot reloading for development
    stdin_open: true
    tty: true

volumes:
  frappe-node-modules:
  roster-node-modules:
  frappe-ui-node-modules:

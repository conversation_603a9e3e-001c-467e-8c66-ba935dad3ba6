---
name: Feature Request
description: Suggest an idea to improve Frappe HR
labels: ["feature-request"]

body:
  - type: markdown
    attributes:
      value: |
        Welcome to Frappe HR issue tracker! Before submitting a request, please consider the following:

        1. This tracker should only be used to report bugs and request features / enhancements to Frappe HR
            - For questions and general support, checkout the [documentation](https://docs.frappe.io/hr) or use the [forum](https://discuss.frappe.io) to get inputs from the open source community.
        2. Use the search function before creating a new issue. Duplicates will be closed and directed to
          the original discussion.
        3. When making a feature request, make sure to be as verbose as possible. The better you convey your message, the greater the drive to make it happen.


        Please keep in mind that we get many many requests and we can't possibly work on all of them, we prioritize development based on the goals of the product and organization. Feature requests are still welcome as it helps us in research when we do decide to work on the requested feature.

        If you're in urgent need to a feature, please try the following channels to get paid developments done quickly:
        1. Certified Frappe partners: https://frappe.io/partners
        2. Developer community on Frappe forums: https://discuss.frappe.io/c/developers/5
        3. Telegram group for Frappe HR development work: https://t.me/frappehr

  - type: textarea
    id: problem-info
    attributes:
      label: Is your feature request related to a problem? Please describe.
      description: A clear and concise description of what the problem is. Eg. I'm always frustrated when [...]
      placeholder: Please provide as much information as possible.

  - type: textarea
    id: solution-info
    attributes:
      label: Describe the solution you'd like
      description: A clear and concise description of what you want to happen.

  - type: textarea
    id: alternatives-info
    attributes:
      label: Describe the alternatives you've considered
      description: A clear and concise description of any alternative solutions or features you've considered.

  - type: textarea
    id: additional-info
    attributes:
      label: Additional context
      description: Add any other context or screenshots about the feature request here.
...
